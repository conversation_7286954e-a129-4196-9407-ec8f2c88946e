{"name": "interview_prep", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@google-cloud/vision": "^5.3.3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@types/lodash": "^4.17.20", "@types/pdf-parse": "^1.1.5", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "mammoth": "^1.10.0", "next": "15.2.4", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.4.149", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "serpapi": "^2.2.1", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "underscore": "^1.13.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}