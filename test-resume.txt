<PERSON>
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: (*************
Location: San Francisco, CA

Professional Summary:
Experienced software engineer with 5+ years of experience in full-stack development. Proficient in JavaScript, React, Node.js, and Python. Strong problem-solving skills and experience with agile development methodologies.

Work Experience:

Senior Software Engineer | Tech Company Inc. | 2021 - Present
• Developed and maintained web applications using React and Node.js
• Led a team of 3 junior developers on multiple projects
• Improved application performance by 40% through code optimization
• Implemented automated testing procedures, reducing bugs by 30%

Software Engineer | StartupXYZ | 2019 - 2021
• Built responsive web applications using modern JavaScript frameworks
• Collaborated with cross-functional teams to deliver features on time
• Participated in code reviews and maintained high code quality standards
• Worked with RESTful APIs and database design

Skills:
• Programming Languages: JavaScript, Python, TypeScript, Java
• Frontend: React, Vue.js, HTML5, CSS3, Sass
• Backend: Node.js, Express.js, Django, Flask
• Databases: PostgreSQL, MongoDB, MySQL
• Tools: Git, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>

Education:
Bachelor of Science in Computer Science
University of California, Berkeley | 2015 - 2019

Certifications:
• AWS Certified Developer Associate
• Google Cloud Professional Developer
