import { NextRequest, NextResponse } from 'next/server';
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import { getCurrentUser } from '@/lib/actions/auth.action';
import { interviewService } from '@/lib/firebase/interview-service';
import { ImageAnnotatorClient } from '@google-cloud/vision';
import mammoth from 'mammoth';

// Initialize Google Vision client with Firebase credentials
const visionClient = new ImageAnnotatorClient({
	projectId: process.env.FIREBASE_PROJECT_ID,
	credentials: {
		client_email: process.env.FIREBASE_CLIENT_EMAIL,
		private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
	},
});

async function extractTextFromFile(file: File): Promise<string> {
	const fileType = file.type;
	const buffer = Buffer.from(await file.arrayBuffer());

	try {
		// Handle different file types
		if (fileType === 'application/pdf') {
			// Try PDF text extraction first
			const pdf = require('pdf-parse');
			const pdfData = await pdf(buffer);

			// If PDF has extractable text, use it
			if (pdfData.text && pdfData.text.trim().length > 50) {
				return pdfData.text;
			}

			// If PDF has little/no text, it might be scanned - use OCR
			console.log(
				'PDF has minimal text, OCR temporarily disabled - using extracted text'
			);
			// TODO: Enable OCR after Vision API is properly configured
			return pdfData.text || '';
		} else if (
			fileType ===
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
		) {
			// Handle DOCX files
			const result = await mammoth.extractRawText({ buffer });
			return result.value;
		} else if (fileType === 'application/msword') {
			// Handle DOC files - mammoth can handle some DOC files
			try {
				const result = await mammoth.extractRawText({ buffer });
				return result.value;
			} catch (error) {
				throw new Error(
					'DOC file format not fully supported. Please convert to DOCX or PDF.'
				);
			}
		} else if (fileType === 'text/plain') {
			// Handle text files
			return file.text();
		} else if (fileType.startsWith('image/')) {
			// Handle image files with OCR
			console.log('Image OCR temporarily disabled');
			// TODO: Enable OCR after Vision API is properly configured
			throw new Error(
				'Image processing is temporarily unavailable. Please upload a PDF, DOC, DOCX, or TXT file instead.'
			);
		} else {
			throw new Error(`Unsupported file type: ${fileType}`);
		}
	} catch (error) {
		console.error('Error extracting text from file:', error);
		throw new Error(
			`Failed to extract text from file: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

export async function POST(request: NextRequest) {
	try {
		// TODO: Re-enable authentication after testing
		const user = await getCurrentUser();
		if (!user) {
			console.log('No authenticated user - proceeding with test mode');
			// For testing purposes, create a mock user
			// return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Skip credit check for testing
		if (user) {
			// Ensure user has resume credits (auto-renews monthly)
			const credits = await interviewService.getResumeCredits(user.id);
			if (credits <= 0) {
				return NextResponse.json(
					{ error: 'No resume review credits remaining' },
					{ status: 402 }
				);
			}
		}

		const formData = await request.formData();
		const file = formData.get('resume') as File;

		if (!file) {
			return NextResponse.json({ error: 'No file provided' }, { status: 400 });
		}

		// Extract text from file using enhanced extraction
		let fileContent: string;
		try {
			console.log(
				`Processing file: ${file.name}, type: ${file.type}, size: ${file.size}`
			);
			fileContent = await extractTextFromFile(file);
			console.log(`Extracted text length: ${fileContent.length}`);

			// Validate that we extracted meaningful content
			if (!fileContent || fileContent.trim().length < 10) {
				return NextResponse.json(
					{
						error:
							'Could not extract meaningful text from the file. Please ensure the file contains readable text.',
					},
					{ status: 400 }
				);
			}
		} catch (extractionError) {
			console.error('Text extraction error:', extractionError);
			return NextResponse.json(
				{
					error:
						extractionError instanceof Error
							? extractionError.message
							: 'Failed to process file',
				},
				{ status: 400 }
			);
		}

		// Generate AI analysis using Gemini
		const result = await generateText({
			model: google('gemini-1.5-flash'),
			prompt: `You are a professional resume reviewer. Analyze the following resume and provide comprehensive feedback.

IMPORTANT: Respond ONLY with a valid JSON object in this exact format (no markdown, no explanations, no additional text):

{
  "overallScore": 85,
  "strengths": ["Specific strength 1", "Specific strength 2", "Specific strength 3"],
  "improvements": ["Specific improvement 1", "Specific improvement 2", "Specific improvement 3"],
  "suggestions": ["Actionable suggestion 1", "Actionable suggestion 2", "Actionable suggestion 3"],
  "summary": "A comprehensive summary of the resume analysis in 2-3 sentences."
}

Resume content:
${fileContent}

Provide detailed, actionable feedback. Focus on:
- Technical skills and experience relevance
- Achievement quantification and impact
- Resume structure and formatting
- Industry-specific keywords
- Areas for improvement

Respond with ONLY the JSON object:`,
		});

		// Parse the AI response
		console.log('Gemini response:', result.text);
		let analysis;
		try {
			// Clean the response by removing markdown code blocks if present
			let cleanedText = result.text.trim();
			if (cleanedText.startsWith('```json')) {
				cleanedText = cleanedText
					.replace(/^```json\s*/, '')
					.replace(/\s*```$/, '');
			} else if (cleanedText.startsWith('```')) {
				cleanedText = cleanedText.replace(/^```\s*/, '').replace(/\s*```$/, '');
			}

			analysis = JSON.parse(cleanedText);
		} catch (parseError) {
			// If JSON parsing fails, create a structured response from the text
			analysis = {
				overallScore: 75,
				strengths: [
					'Resume contains relevant work experience',
					'Skills section is present',
					'Contact information is included',
				],
				improvements: [
					'Could benefit from more specific achievements',
					'Consider adding quantified results',
					'Review formatting for consistency',
				],
				suggestions: [
					'Add specific metrics and achievements to your experience',
					'Use action verbs to start each bullet point',
					'Ensure consistent formatting throughout',
					'Include relevant keywords for your target role',
					'Consider adding a professional summary section',
				],
				summary: result.text.substring(0, 200) + '...',
			};
		}

		// Validate the response structure
		if (
			!analysis.overallScore ||
			!analysis.strengths ||
			!analysis.improvements ||
			!analysis.suggestions ||
			!analysis.summary
		) {
			throw new Error('Invalid analysis structure');
		}

		// Deduct a resume review credit after successful analysis
		if (user) {
			await interviewService.deductResumeCredit(user.id);
		}

		return NextResponse.json(analysis);
	} catch (error) {
		console.error('Error analyzing resume:', error);
		return NextResponse.json(
			{ error: 'Failed to analyze resume' },
			{ status: 500 }
		);
	}
}
